#!/bin/bash
# Convenient wrapper for viewing training data in human-readable format

echo "📖 Training Data Viewer"
echo "======================"

# Check if we're in the right directory
if [ ! -d "scripts" ] || [ ! -d "data" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "   Expected structure: scripts/ and data/ directories"
    exit 1
fi

# Check if training data exists
if [ ! -f "data/cursor_prediction_training_data_clean.jsonl" ]; then
    echo "❌ Error: Training data file not found"
    echo "   Expected: data/cursor_prediction_training_data_clean.jsonl"
    echo "   💡 Tip: Run ./run_complete_pipeline.sh first to generate training data"
    exit 1
fi

# Show file info
file_size=$(ls -lh data/cursor_prediction_training_data_clean.jsonl | awk '{print $5}')
line_count=$(wc -l < data/cursor_prediction_training_data_clean.jsonl)
echo "📁 Training data file: data/cursor_prediction_training_data_clean.jsonl"
echo "   📊 Size: $file_size"
echo "   📈 Samples: $line_count"
echo ""

# Show options
echo "🎯 Viewing Options:"
echo "   1. Preview (first 5 samples)"
echo "   2. Function Parameters scenario only"
echo "   3. Renaming scenario only"
echo "   4. All samples"
echo "   5. Custom range"
echo "   6. Custom options"
echo ""

read -p "Choose an option (1-6): " choice

case $choice in
    1)
        echo "🔍 Generating preview (first 5 samples)..."
        python3 scripts/format_for_reading.py --preview
        output_file="data/cursor_prediction_training_data_clean_readable.txt"
        ;;
    2)
        echo "🔧 Generating Function Parameters scenario view..."
        python3 scripts/format_for_reading.py -s function_parameters -o data/function_parameters_readable.txt
        output_file="data/function_parameters_readable.txt"
        ;;
    3)
        echo "🏷️  Generating Renaming scenario view..."
        python3 scripts/format_for_reading.py -s function_variable_renaming -o data/renaming_scenario_readable.txt
        output_file="data/renaming_scenario_readable.txt"
        ;;
    4)
        echo "📚 Generating complete view (all samples)..."
        echo "⚠️  Warning: This will create a large file (~10-20 MB)"
        read -p "Continue? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            python3 scripts/format_for_reading.py
            output_file="data/cursor_prediction_training_data_clean_readable.txt"
        else
            echo "❌ Cancelled by user"
            exit 0
        fi
        ;;
    5)
        echo "📊 Custom range selection:"
        read -p "Start from sample number (1-$line_count): " start_num
        read -p "Number of samples to show: " num_samples
        
        # Convert to 0-based index
        start_index=$((start_num - 1))
        
        if [ $start_index -lt 0 ] || [ $start_index -ge $line_count ]; then
            echo "❌ Invalid start number. Must be between 1 and $line_count"
            exit 1
        fi
        
        echo "📖 Generating custom range view (samples $start_num to $((start_num + num_samples - 1)))..."
        python3 scripts/format_for_reading.py --start $start_index -n $num_samples -o data/custom_range_readable.txt
        output_file="data/custom_range_readable.txt"
        ;;
    6)
        echo "🛠️  Custom options:"
        echo "Usage: python3 scripts/format_for_reading.py [options]"
        echo ""
        echo "Options:"
        echo "  -o OUTPUT          Output file"
        echo "  -n MAX_SAMPLES     Maximum samples to convert"
        echo "  -s SCENARIO        Filter by scenario (function_parameters or function_variable_renaming)"
        echo "  --start INDEX      Start from sample index (0-based)"
        echo "  --preview          Preview first 5 samples"
        echo ""
        echo "Examples:"
        echo "  python3 scripts/format_for_reading.py --preview"
        echo "  python3 scripts/format_for_reading.py -s function_parameters -n 10"
        echo "  python3 scripts/format_for_reading.py --start 100 -n 20"
        exit 0
        ;;
    *)
        echo "❌ Invalid option. Please choose 1-6."
        exit 1
        ;;
esac

# Check if conversion was successful
if [ $? -eq 0 ] && [ -f "$output_file" ]; then
    echo ""
    echo "✅ Conversion completed successfully!"
    echo "📖 Output file: $output_file"
    
    # Show file size
    output_size=$(ls -lh "$output_file" | awk '{print $5}')
    echo "📊 Output size: $output_size"
    
    echo ""
    echo "💡 Tips for viewing:"
    echo "   - Use 'less $output_file' for paged viewing"
    echo "   - Use 'head -50 $output_file' to see the beginning"
    echo "   - Search for specific patterns with 'grep'"
    echo "   - Open in your favorite text editor"
else
    echo "❌ Conversion failed"
    exit 1
fi
