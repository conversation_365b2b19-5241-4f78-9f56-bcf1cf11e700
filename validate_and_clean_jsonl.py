#!/usr/bin/env python3
"""
Validate and clean the merged JSONL file
Combines validation and cleaning functionality in one script
"""

import json
import os
from collections import Counter

def validate_and_clean_jsonl(input_file: str, output_file: str = None):
    """Validate JSONL file and optionally create a cleaned version"""
    print(f"🔍 Validating and cleaning {input_file}...")
    
    if not os.path.exists(input_file):
        print(f"❌ File {input_file} not found")
        return
    
    total_lines = 0
    valid_lines = 0
    scenarios = Counter()
    errors = []
    
    required_fields = ['current_version', 'next_version', 'scenario']
    
    # If output file specified, prepare for cleaning
    cleaned_data = []
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                total_lines += 1
                
                try:
                    # Parse JSON
                    data = json.loads(line.strip())
                    
                    # Check required fields
                    missing_fields = [field for field in required_fields if field not in data]
                    if missing_fields:
                        errors.append(f"Line {line_num}: Missing fields: {missing_fields}")
                        continue
                    
                    # Check cursor markers
                    has_current_cursor = '<|cursor|>' in data.get('current_version', '')
                    has_next_cursor = '<|cursor|>' in data.get('next_version', '')
                    
                    if not has_current_cursor or not has_next_cursor:
                        missing = []
                        if not has_current_cursor:
                            missing.append('current_version')
                        if not has_next_cursor:
                            missing.append('next_version')
                        errors.append(f"Line {line_num}: Missing cursor in {', '.join(missing)}")
                        continue
                    
                    # Valid entry
                    scenarios[data['scenario']] += 1
                    valid_lines += 1
                    
                    # Add to cleaned data if output file specified
                    if output_file:
                        cleaned_data.append(data)
                    
                except json.JSONDecodeError as e:
                    errors.append(f"Line {line_num}: JSON decode error: {e}")
                
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return
    
    # Report validation results
    print(f"\n📊 Validation Results:")
    print(f"  Total lines: {total_lines}")
    print(f"  Valid lines: {valid_lines}")
    print(f"  Invalid lines: {total_lines - valid_lines}")
    
    print(f"\n📈 Scenario Distribution:")
    for scenario, count in scenarios.items():
        percentage = (count / valid_lines * 100) if valid_lines > 0 else 0
        print(f"  {scenario}: {count} samples ({percentage:.1f}%)")
    
    if errors:
        print(f"\n❌ Errors found ({len(errors)} total):")
        for error in errors[:10]:  # Show first 10 errors
            print(f"  {error}")
        if len(errors) > 10:
            print(f"  ... and {len(errors) - 10} more errors")
    else:
        print(f"\n✅ All lines are valid!")
    
    # Save cleaned data if output file specified
    if output_file and cleaned_data:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                for item in cleaned_data:
                    json.dump(item, f, ensure_ascii=False)
                    f.write('\n')
            
            file_size = os.path.getsize(output_file)
            print(f"\n🧹 Cleaning Results:")
            print(f"  ✅ Cleaned file saved as: {output_file}")
            print(f"  📁 File size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
            print(f"  📊 Final dataset: {len(cleaned_data)} valid training samples")
            
        except Exception as e:
            print(f"❌ Error saving cleaned file: {e}")
    
    # Summary
    print(f"\n🎯 Summary:")
    if valid_lines == total_lines and total_lines > 0:
        print(f"  ✅ Perfect! {total_lines} valid training samples ready for use")
    else:
        print(f"  ⚠️  {valid_lines}/{total_lines} lines are valid")
        if output_file:
            print(f"  🧹 Cleaned version created with {len(cleaned_data)} valid samples")

def main():
    import sys
    
    # Default behavior: validate and clean
    input_file = "cursor_prediction_training_data.jsonl"
    output_file = "cursor_prediction_training_data_clean.jsonl"
    
    # Check command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--validate-only":
            # Only validate, don't create cleaned file
            output_file = None
        elif sys.argv[1] == "--help":
            print("Usage:")
            print("  python3 validate_and_clean_jsonl.py              # Validate and create cleaned file")
            print("  python3 validate_and_clean_jsonl.py --validate-only  # Only validate, no cleaning")
            print("  python3 validate_and_clean_jsonl.py --help           # Show this help")
            return
    
    validate_and_clean_jsonl(input_file, output_file)

if __name__ == "__main__":
    main()
