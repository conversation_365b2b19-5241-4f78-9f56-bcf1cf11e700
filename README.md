# Cursor Prediction Training Data Generator

This project contains scripts to generate training data for cursor prediction models in two different scenarios.

## 📁 File Structure

### 🔧 Function Parameters Scenario (函数修改参数场景)
Files without `_2` suffix are for the function parameter modification scenario.

**Python Scripts:**
- `generate_function_params.py` - Data generation script
- `process_function_params.py` - **Integrated processing script** ⭐

**Data Files:**
- `api_responses_function_params.json` - Raw generated data
- `api_responses_function_params_final.json` - **Final processed data** ⭐

### 🏷️ Function/Variable Renaming Scenario (函数/变量重命名场景)
Files with `_2` suffix are for the function/variable renaming scenario.

**Python Scripts:**
- `generate_rename_2.py` - Data generation script
- `process_rename_2.py` - **Integrated processing script** ⭐

**Data Files:**
- `api_responses_rename_2.json` - Raw generated data
- `api_responses_rename_2_final.json` - **Final processed data** ⭐

### 🔄 Merge and Conversion
- `merge_to_jsonl.py` - **Merge script** ⭐ (combines final JSON files → JSONL)
- `validate_jsonl.py` - **Validation script** (checks JSONL format and data integrity)
- `clean_jsonl.py` - **Cleaning script** (removes invalid entries)
- `cursor_prediction_training_data.jsonl` - Merged training data (1,795 samples)
- `cursor_prediction_training_data_clean.jsonl` - **Final cleaned training data** ⭐ (1,785 samples)

## 🚀 Usage Instructions

### For Function Parameters Scenario:
```bash
# 1. Generate raw data
python3 generate_function_params.py

# 2. Process data (clean + deduplicate + similarity removal)
python3 process_function_params.py

# Final result: api_responses_function_params_final.json
```

### For Renaming Scenario:
```bash
# 1. Generate raw data
python3 generate_rename_2.py

# 2. Process data (clean + deduplicate + similarity removal)
python3 process_rename_2.py

# Final result: api_responses_rename_2_final.json
```

### 🎯 **Complete Pipeline (Recommended)**:
```bash
# 1. Generate and process both scenarios
python3 process_function_params.py
python3 process_rename_2.py

# 2. Merge and convert to JSONL format
python3 merge_to_jsonl.py

# 3. Validate and clean the data
python3 validate_jsonl.py
python3 clean_jsonl.py

# Final result: cursor_prediction_training_data_clean.jsonl (1,785 samples)
```

## 📊 Data Format

### JSON Format (Individual Files)
Each JSON file contains an array of training samples:

```json
[
  {
    "current_version": "function add(a: number, b: number, c: number): number {\n    return a + b + c;\n} <|cursor|>\n\nconst result = add(1, 2);",
    "next_version": "function add(a: number, b: number, c: number): number {\n    return a + b + c;\n}\n\nconst result = add(1, 2<|cursor|>);"
  }
]
```

### JSONL Format (Merged Training Data) ⭐
The merged training data file uses JSONL format (one JSON object per line) with scenario labels:

```jsonl
{"current_version": "function add(a: number, b: number, c: number): number {\n    return a + b + c;\n} <|cursor|>\n\nconst result = add(1, 2);", "next_version": "function add(a: number, b: number, c: number): number {\n    return a + b + c;\n}\n\nconst result = add(1, 2<|cursor|>);", "scenario": "function_parameters"}
{"current_version": "const calculateSum = (a, b) => a + b;<|cursor|>\n\nconst result = calculate(5, 10);", "next_version": "const calculateSum = (a, b) => a + b;\n\nconst result = <|cursor|>calculate(5, 10);", "scenario": "function_variable_renaming"}
```

**Scenario Labels:**
- `function_parameters`: Function parameter modification scenarios (876 samples)
- `function_variable_renaming`: Function/variable renaming scenarios (909 samples)

## 🤖 API Generation Instructions

The data generation scripts use the following prompt template for the AI model:

```
You need to complete current_code and next_code according to the code language and scenario, and give the following data in json format for training the cursor prediction model.
The cursor position needs to be shown in the code, indicated by <|cursor|>.
The code should be in multiple lines.
The code content in `current_code` and `next_code` should be the same, but the cursor position is different. `current_code` represents the current cursor position, and `next_code` represents the predicted cursor position.
The code content should be as complex as possible and possess uniqueness.

Code Language: TypeScript
Scenario: <Scene Description>

Reply format
{
"current_version": "current_code",
"next_version": "next_code"
}
```

### Scene Descriptions Used:

**Scene 1 (Function Parameters):**
```
Function Arguments,After adding or removing function parameters(This means the cursor should now be positioned after the newly modified function definition.), the original way of calling the function is no longer appropriate. The cursor should jump to the location of the code that calls the function, so that the user can modify the function call.
```

**Scene 2 (Renaming):**
```
Renaming functions/variables,After renaming a function/variable (This means the cursor should be positioned after the newly modified function/variable name at this point.), The original use of this function/variable elsewhere is no longer correct. The cursor should jump to the other code location where the function/variable is used so that the user can correctly change the function/variable name.Therefore, `current_code` should be the state of the code after a defined function/variable name has just been changed. `next_code` should be the prediction of the cursor jumping to other locations where the function/variable was used before the name change.
```

## 🎯 Scenarios Explained

### Scene 1: Function Parameters Scenario
**Scenario Description**: Function Arguments, After adding or removing function parameters (This means the cursor should now be positioned after the newly modified function definition.), the original way of calling the function is no longer appropriate. The cursor should jump to the location of the code that calls the function, so that the user can modify the function call.

- **Trigger**: After adding or removing function parameters
- **Current Position**: After the modified function definition
- **Next Position**: At the function call site that needs to be updated
- **Use Case**: Help users update function calls when parameters change

### Scene 2: Renaming Scenario
**Scenario Description**: Renaming functions/variables, After renaming a function/variable (This means the cursor should be positioned after the newly modified function/variable name at this point.), The original use of this function/variable elsewhere is no longer correct. The cursor should jump to the other code location where the function/variable is used so that the user can correctly change the function/variable name. Therefore, `current_code` should be the state of the code after a defined function/variable name has just been changed. `next_code` should be the prediction of the cursor jumping to other locations where the function/variable was used before the name change.

- **Trigger**: After renaming a function or variable
- **Current Position**: After the renamed function/variable definition
- **Next Position**: At other locations where the old name is still used
- **Use Case**: Help users update all references when renaming

## 📈 Integrated Processing Pipeline

The `process_*.py` scripts perform all processing steps in one command:

1. **Data Cleaning** → Remove invalid items (raw_content, missing fields)
2. **Exact Duplicate Removal** → Remove identical items
3. **High Similarity Removal** → Remove items with >98% similarity
4. **Final Analysis** → Report remaining items with >90% similarity
5. **Final Result** → High-quality unique training samples

### Processing Features:
- ✅ **One-step processing** - No intermediate files
- ✅ **Detailed progress reporting** - See each step's results
- ✅ **Smart similarity detection** - Advanced content comparison
- ✅ **Performance optimized** - Efficient algorithms

## 🔧 Configuration

Each script has configurable parameters at the top:
- `NUM_RUNS`: Number of API calls to make
- `DELAY_BETWEEN_CALLS`: Delay between API calls
- File paths for input/output

## 📝 Current Status

- ✅ **Function Parameters**: Complete pipeline with 877 final samples (12.5% reduction)
- ✅ **Renaming**: Complete pipeline with 918 final samples (11.6% reduction)

## 🎉 Final Training Data Files

### Individual JSON Files:
- **Function Parameters**: `api_responses_function_params_final.json` (877 samples)
- **Renaming**: `api_responses_rename_2_final.json` (918 samples)

### 🚀 **Merged JSONL Training Data**: `cursor_prediction_training_data_clean.jsonl` ⭐
- **Total samples**: 1,785 (876 function_parameters + 909 renaming)
- **Format**: JSONL (JSON Lines) - one JSON object per line
- **File size**: ~1.3 MB
- **Quality**: 100% valid samples with proper cursor markers
- **Scenario labels**: Each sample includes a `scenario` field for easy filtering

## 📊 Processing Results Summary

| Scenario | Original | Invalid | Duplicates | Similar | Final | Reduction |
|----------|----------|---------|------------|---------|-------|-----------|
| Function Parameters | 1002 | 8 | 116 | 1 | **877** | 12.5% |
| Renaming | 1039 | 71 | 47 | 3 | **918** | 11.6% |
| **Combined Total** | **2041** | **79** | **163** | **4** | **1795** | **12.1%** |
| **Final Clean** | **1795** | **0** | **0** | **10** | **1785** | **0.6%** |

## 🔄 Merge and Convert to JSONL

To merge the final JSON files and convert to JSONL format:

```bash
python3 merge_to_jsonl.py
```

This script:
- ✅ Loads both final JSON files
- ✅ Adds scenario labels (`function_parameters` and `function_variable_renaming`)
- ✅ Merges all data into a single dataset
- ✅ Converts to JSONL format for easy streaming and processing
- ✅ Provides detailed progress reporting

## 🔍 Data Validation and Cleaning

### Validate JSONL Data:
```bash
python3 validate_jsonl.py
```

This validation script:
- ✅ Checks JSONL format integrity
- ✅ Validates required fields (`current_version`, `next_version`, `scenario`)
- ✅ Ensures cursor markers (`<|cursor|>`) are present in both versions
- ✅ Reports scenario distribution and statistics
- ✅ Identifies any problematic entries

### Clean Invalid Entries:
```bash
python3 clean_jsonl.py
```

This cleaning script:
- ✅ Removes entries missing cursor markers
- ✅ Filters out malformed JSON entries
- ✅ Creates a clean dataset with 100% valid samples
- ✅ Reports detailed cleaning statistics
