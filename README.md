# Cursor Prediction Training Data Generator

This project contains scripts to generate training data for cursor prediction models in two different scenarios.

**🗂️ Organized Structure**: Scripts and data are cleanly separated for better project organization and maintainability.

## 📁 File Structure

```
📦 data_generate_cursor_predict/
├── 📄 README.md
├── 🚀 run_complete_pipeline.sh    # ⭐ Complete pipeline script
├── 📖 view_training_data.sh       # ⭐ Human-readable data viewer
├── 📁 scripts/                    # Python scripts
│   ├── generate_function_params.py
│   ├── process_function_params.py
│   ├── generate_rename_2.py
│   ├── process_rename_2.py
│   ├── merge_to_jsonl.py          # ⭐ All-in-one processing
│   └── format_for_reading.py      # ⭐ JSONL to readable format
└── 📁 data/                       # Data files
    ├── api_responses_function_params.json      # Raw data (generated)
    ├── api_responses_function_params_final.json # Processed data
    ├── api_responses_rename_2.json             # Raw data (generated)
    ├── api_responses_rename_2_final.json       # Processed data
    └── cursor_prediction_training_data_clean.jsonl  # ⭐ Final output
```

### 🔧 Function Parameters <PERSON>enario (函数修改参数场景)
Files without `_2` suffix are for the function parameter modification scenario.

**Scripts:**
- `scripts/generate_function_params.py` - Data generation script
- `scripts/process_function_params.py` - **Integrated processing script** ⭐

**Data Files:**
- `data/api_responses_function_params.json` - Raw generated data
- `data/api_responses_function_params_final.json` - **Final processed data** ⭐

### 🏷️ Function/Variable Renaming Scenario (函数/变量重命名场景)
Files with `_2` suffix are for the function/variable renaming scenario.

**Scripts:**
- `scripts/generate_rename_2.py` - Data generation script
- `scripts/process_rename_2.py` - **Integrated processing script** ⭐

**Data Files:**
- `data/api_responses_rename_2.json` - Raw generated data
- `data/api_responses_rename_2_final.json` - **Final processed data** ⭐

### 🔄 Merge, Validation & Cleaning
**Script:**
- `scripts/merge_to_jsonl.py` - **All-in-one processing script** ⭐ (merge → validate → clean)

**Final Output:**
- `data/cursor_prediction_training_data_clean.jsonl` - **Final training data** ⭐ (1,785 samples)

### 📖 Data Viewing & Analysis
**Scripts:**
- `view_training_data.sh` - **Interactive data viewer** ⭐ (user-friendly interface)
- `scripts/format_for_reading.py` - **JSONL to readable format converter** ⭐ (command-line tool)

**Features:**
- ✅ **Human-readable format** - Convert JSONL to clean, readable text
- ✅ **Interactive viewer** - Easy-to-use menu interface
- ✅ **Smart scenario detection** - Automatically infers scenario types from data patterns
- ✅ **Scenario filtering** - View specific scenarios only (with auto-detection)
- ✅ **Sample limiting** - Preview or custom ranges
- ✅ **Cursor highlighting** - Clear visual indicators (🔸CURSOR🔸)

### 🎯 **Structure Benefits:**
- ✅ **Clean Separation** - Scripts and data are organized separately
- ✅ **Easy Navigation** - Clear distinction between code and output
- ✅ **Scalable** - Easy to add new scenarios or data types
- ✅ **Maintainable** - Simplified project structure for development
- ✅ **Version Control Friendly** - Better .gitignore management for data vs code

## 📊 Data Generation

### Raw Data Requirements
The pipeline requires raw data files to be generated first through API calls:

- `data/api_responses_function_params.json` - Function parameter scenarios (~1000 samples)
- `data/api_responses_rename_2.json` - Function/variable renaming scenarios (~1000 samples)

### Generation Process
- **Time Estimate**: 10-30 minutes total (depending on API response time)
- **API Calls**: ~2000 total calls (1000 per scenario)
- **Requirements**: Proper API access configuration

### Automatic Generation
The `run_complete_pipeline.sh` script automatically:
- ✅ **Detects missing data** - Checks for required raw data files
- ✅ **Provides time estimates** - Shows expected generation time (10-30 minutes)
- ✅ **Offers manual alternatives** - Suggests individual generation commands
- ✅ **Shows existing data stats** - Displays file sizes and item counts when data exists

## 🚀 Usage Instructions

### For Function Parameters Scenario:
```bash
# 1. Generate raw data
python3 scripts/generate_function_params.py

# 2. Process data (clean + deduplicate + similarity removal)
python3 scripts/process_function_params.py

# Final result: data/api_responses_function_params_final.json
```

### For Renaming Scenario:
```bash
# 1. Generate raw data
python3 scripts/generate_rename_2.py

# 2. Process data (clean + deduplicate + similarity removal)
python3 scripts/process_rename_2.py

# Final result: data/api_responses_rename_2_final.json
```

### 🎯 **Complete Pipeline (Recommended)**:

**Option 1: Manual Steps**
```bash
# 1. Generate and process both scenarios
python3 scripts/process_function_params.py
python3 scripts/process_rename_2.py

# 2. Merge, validate, and clean in one step
python3 scripts/merge_to_jsonl.py

# Final result: data/cursor_prediction_training_data_clean.jsonl (1,785 samples)
```

**Option 2: Automated Pipeline** ⭐
```bash
# Run complete pipeline with automatic data generation if needed
./run_complete_pipeline.sh

# The script will:
# 1. Check for raw data files
# 2. Generate missing data if needed (with time estimates)
# 3. Process and clean all data
# 4. Create final training dataset

# Final result: data/cursor_prediction_training_data_clean.jsonl (1,785 samples)
```

## 📖 Viewing Training Data

After generating the training data, you can view it in human-readable format:

### Interactive Viewer (Recommended) ⭐
```bash
./view_training_data.sh

# Interactive menu with options:
# 1. Preview (first 5 samples)
# 2. Function Parameters scenario only
# 3. Renaming scenario only
# 4. All samples
# 5. Custom range
# 6. Custom options
```

### Command Line Tool
```bash
# Preview first 5 samples
python3 scripts/format_for_reading.py --preview

# View specific scenario
python3 scripts/format_for_reading.py -s function_parameters -n 10

# Custom range (samples 100-120)
python3 scripts/format_for_reading.py --start 99 -n 20

# All options
python3 scripts/format_for_reading.py --help
```

### Output Format
The readable format shows each sample with:
- **Sample number and scenario type**
- **Current version** with cursor position (🔸CURSOR🔸)
- **Next version** with predicted cursor position
- **Clean code formatting** for easy review

## 📊 Data Format

### JSON Format (Individual Files)
Each JSON file contains an array of training samples:

```json
[
  {
    "current_version": "function add(a: number, b: number, c: number): number {\n    return a + b + c;\n} <|cursor|>\n\nconst result = add(1, 2);",
    "next_version": "function add(a: number, b: number, c: number): number {\n    return a + b + c;\n}\n\nconst result = add(1, 2<|cursor|>);"
  }
]
```

### JSONL Format (Merged Training Data) ⭐
The merged training data file uses pure JSONL format (one JSON object per line) with only essential training fields:

```jsonl
{"current_version": "function add(a: number, b: number, c: number): number {\n    return a + b + c;\n} <|cursor|>\n\nconst result = add(1, 2);", "next_version": "function add(a: number, b: number, c: number): number {\n    return a + b + c;\n}\n\nconst result = add(1, 2<|cursor|>);"}
{"current_version": "const calculateSum = (a, b) => a + b;<|cursor|>\n\nconst result = calculate(5, 10);", "next_version": "const calculateSum = (a, b) => a + b;\n\nconst result = <|cursor|>calculate(5, 10);"}
```

**Pure Training Format:**
- ✅ **Clean data**: Only `current_version` and `next_version` fields
- ✅ **No metadata**: No scenario labels or extra fields in training data
- ✅ **ML-ready**: Direct input for machine learning models
- ✅ **Scenario inference**: Viewing tools can automatically infer scenario types

## 🤖 API Generation Instructions

The data generation scripts use the following prompt template for the AI model:

```
You need to complete current_code and next_code according to the code language and scenario, and give the following data in json format for training the cursor prediction model.
The cursor position needs to be shown in the code, indicated by <|cursor|>.
The code should be in multiple lines.
The code content in `current_code` and `next_code` should be the same, but the cursor position is different. `current_code` represents the current cursor position, and `next_code` represents the predicted cursor position.
The code content should be as complex as possible and possess uniqueness.

Code Language: TypeScript
Scenario: <Scene Description>

Reply format
{
"current_version": "current_code",
"next_version": "next_code"
}
```

### Scene Descriptions Used:

**Scene 1 (Function Parameters):**
```
Function Arguments,After adding or removing function parameters(This means the cursor should now be positioned after the newly modified function definition.), the original way of calling the function is no longer appropriate. The cursor should jump to the location of the code that calls the function, so that the user can modify the function call.
```

**Scene 2 (Renaming):**
```
Renaming functions/variables,After renaming a function/variable (This means the cursor should be positioned after the newly modified function/variable name at this point.), The original use of this function/variable elsewhere is no longer correct. The cursor should jump to the other code location where the function/variable is used so that the user can correctly change the function/variable name.Therefore, `current_code` should be the state of the code after a defined function/variable name has just been changed. `next_code` should be the prediction of the cursor jumping to other locations where the function/variable was used before the name change.
```

## 🎯 Scenarios Explained

### Scene 1: Function Parameters Scenario
**Scenario Description**: Function Arguments, After adding or removing function parameters (This means the cursor should now be positioned after the newly modified function definition.), the original way of calling the function is no longer appropriate. The cursor should jump to the location of the code that calls the function, so that the user can modify the function call.

- **Trigger**: After adding or removing function parameters
- **Current Position**: After the modified function definition
- **Next Position**: At the function call site that needs to be updated
- **Use Case**: Help users update function calls when parameters change

### Scene 2: Renaming Scenario
**Scenario Description**: Renaming functions/variables, After renaming a function/variable (This means the cursor should be positioned after the newly modified function/variable name at this point.), The original use of this function/variable elsewhere is no longer correct. The cursor should jump to the other code location where the function/variable is used so that the user can correctly change the function/variable name. Therefore, `current_code` should be the state of the code after a defined function/variable name has just been changed. `next_code` should be the prediction of the cursor jumping to other locations where the function/variable was used before the name change.

- **Trigger**: After renaming a function or variable
- **Current Position**: After the renamed function/variable definition
- **Next Position**: At other locations where the old name is still used
- **Use Case**: Help users update all references when renaming

## 📈 Integrated Processing Pipeline

### Individual Scenario Processing (`process_*.py`):
1. **Data Cleaning** → Remove invalid items (raw_content, missing fields)
2. **Exact Duplicate Removal** → Remove identical items
3. **High Similarity Removal** → Remove items with >98% similarity
4. **Final Analysis** → Report remaining items with >90% similarity
5. **Final Result** → High-quality unique training samples

### Complete Pipeline Processing (`merge_to_jsonl.py`):
1. **Data Loading** → Load both final JSON files
2. **Scenario Labeling** → Add scenario identifiers
3. **Data Merging** → Combine into single dataset
4. **Format Conversion** → Convert to JSONL format
5. **Data Validation** → Check integrity and required fields
6. **Quality Cleaning** → Remove entries with missing cursor markers
7. **Final Output** → Generate cleaned training dataset

### Processing Features:
- ✅ **Complete automation** - Full pipeline in minimal commands
- ✅ **Detailed progress reporting** - See each step's results
- ✅ **Smart similarity detection** - Advanced content comparison
- ✅ **Quality assurance** - Automatic validation and cleaning
- ✅ **Performance optimized** - Efficient algorithms

## 🔧 Configuration

Each script has configurable parameters at the top:
- `NUM_RUNS`: Number of API calls to make
- `DELAY_BETWEEN_CALLS`: Delay between API calls
- File paths automatically configured for `data/` directory structure

## 📝 Current Status

- ✅ **Function Parameters**: Complete pipeline with 877 final samples (12.5% reduction)
- ✅ **Renaming**: Complete pipeline with 918 final samples (11.6% reduction)
- ✅ **Integrated Pipeline**: All-in-one merge, validation, and cleaning (1,785 final samples)
- ✅ **Organized Structure**: Clean separation of scripts and data for better maintainability
- ✅ **Automated Workflow**: One-command pipeline execution with error handling
- ✅ **Smart Data Detection**: Automatic raw data checking with generation prompts
- ✅ **Time Estimation**: Clear time estimates for data generation (10-30 minutes)
- ✅ **Human-Readable Viewing**: Interactive and command-line tools for data inspection
- ✅ **Production Ready**: Comprehensive validation and quality assurance

## 🎉 Final Training Data Files

### Individual JSON Files:
- **Function Parameters**: `api_responses_function_params_final.json` (877 samples)
- **Renaming**: `api_responses_rename_2_final.json` (918 samples)

### 🚀 **Final JSONL Training Data**: `data/cursor_prediction_training_data_clean.jsonl` ⭐
- **Total samples**: 1,573 high-quality samples
- **Format**: JSONL (JSON Lines) - one JSON object per line
- **File size**: 1.1 MB
- **Quality**: 100% valid samples with proper cursor markers
- **Content consistency**: Code content identical between versions (excluding cursor position)
- **Processing**: Automatically merged, validated, and cleaned with strict quality checks

## 📊 Processing Results Summary

| Scenario | Original | Invalid | Duplicates | Similar | Final | Reduction |
|----------|----------|---------|------------|---------|-------|-----------|
| Function Parameters | 1002 | 8 | 116 | 1 | **877** | 12.5% |
| Renaming | 1039 | 71 | 47 | 3 | **918** | 11.6% |
| **Combined Total** | **2041** | **79** | **163** | **4** | **1795** | **12.1%** |
| **Final Clean** | **1795** | **0** | **0** | **10** | **1785** | **0.6%** |



