#!/usr/bin/env python3
"""
Integrated processing script for function parameters scenario.
Performs cleaning, deduplication, and advanced similarity removal in one step.
"""

import json
import hashlib
import os
import re

# Configuration
INPUT_FILE = "api_responses_function_params.json"
OUTPUT_FILE = "api_responses_function_params_final.json"

# Similarity thresholds
EXACT_DUPLICATE_THRESHOLD = 1.0  # Exact matches
HIGH_SIMILARITY_THRESHOLD = 0.98  # Very similar items to remove
ANALYSIS_SIMILARITY_THRESHOLD = 0.90  # Items to report but not remove

def load_data(filename):
    """Load JSON data from file"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ Error: File '{filename}' not found.")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Error: Invalid <PERSON><PERSON><PERSON> in '{filename}': {e}")
        return None
    except Exception as e:
        print(f"❌ Error loading '{filename}': {e}")
        return None

def is_valid_item(item):
    """Check if an item has both current_version and next_version fields"""
    if not isinstance(item, dict):
        return False
    
    # Must have both current_version and next_version
    has_current = "current_version" in item
    has_next = "next_version" in item
    
    # Must not have raw_content (indicates parsing failure)
    has_raw = "raw_content" in item
    
    # Check if the values are non-empty strings
    current_valid = has_current and isinstance(item["current_version"], str) and item["current_version"].strip()
    next_valid = has_next and isinstance(item["next_version"], str) and item["next_version"].strip()
    
    return current_valid and next_valid and not has_raw

def get_invalid_reason(item):
    """Get the reason why an item is invalid"""
    if not isinstance(item, dict):
        return "Not a dictionary"
    
    if "raw_content" in item:
        return "Contains raw_content (JSON parsing failed)"
    
    if "current_version" not in item:
        return "Missing current_version field"
    
    if "next_version" not in item:
        return "Missing next_version field"
    
    if not isinstance(item.get("current_version"), str) or not item["current_version"].strip():
        return "Invalid or empty current_version"
    
    if not isinstance(item.get("next_version"), str) or not item["next_version"].strip():
        return "Invalid or empty next_version"
    
    return "Unknown reason"

def clean_data(data):
    """Clean the data by filtering valid items"""
    if not isinstance(data, list):
        print("❌ Error: Data should be a list of items")
        return [], []
    
    valid_items = []
    invalid_items = []
    
    for i, item in enumerate(data):
        if is_valid_item(item):
            valid_items.append(item)
        else:
            invalid_items.append({
                "index": i,
                "item": item,
                "reason": get_invalid_reason(item)
            })
    
    return valid_items, invalid_items

def normalize_code(code):
    """Normalize code for comparison"""
    if not code:
        return ""
    
    # Remove cursor markers
    code = re.sub(r'<\|cursor\|>', '', code)
    
    # Normalize whitespace
    code = re.sub(r'\s+', ' ', code.strip())
    
    # Remove comments for comparison
    code = re.sub(r'//.*$', '', code, flags=re.MULTILINE)
    code = re.sub(r'/\*.*?\*/', '', code, flags=re.DOTALL)
    
    return code.strip()

def create_content_signature(item):
    """Create a signature for content comparison"""
    current = normalize_code(item.get("current_version", ""))
    next_ver = normalize_code(item.get("next_version", ""))
    
    # Create a combined signature
    signature = f"{current}|{next_ver}"
    return hashlib.md5(signature.encode('utf-8')).hexdigest()

def calculate_detailed_similarity(item1, item2):
    """Calculate detailed similarity between two items"""
    current1 = item1.get("current_version", "")
    current2 = item2.get("current_version", "")
    next1 = item1.get("next_version", "")
    next2 = item2.get("next_version", "")
    
    # Normalize for comparison
    norm_current1 = normalize_code(current1)
    norm_current2 = normalize_code(current2)
    norm_next1 = normalize_code(next1)
    norm_next2 = normalize_code(next2)
    
    # Calculate character-level similarity
    def char_similarity(s1, s2):
        if not s1 and not s2:
            return 1.0
        if not s1 or not s2:
            return 0.0
        
        min_len = min(len(s1), len(s2))
        max_len = max(len(s1), len(s2))
        
        if max_len == 0:
            return 1.0
        
        # Count matching characters
        matches = sum(1 for i in range(min_len) if s1[i] == s2[i])
        
        # Calculate similarity considering length difference
        similarity = (matches / max_len) * (min_len / max_len)
        return similarity
    
    current_sim = char_similarity(norm_current1, norm_current2)
    next_sim = char_similarity(norm_next1, norm_next2)
    
    # Overall similarity
    overall_sim = (current_sim + next_sim) / 2
    
    return {
        "overall": overall_sim,
        "current": current_sim,
        "next": next_sim
    }

def advanced_deduplicate(data):
    """Advanced deduplication with similarity analysis"""
    if not isinstance(data, list):
        print("❌ Error: Data should be a list of items")
        return [], [], []
    
    print(f"🔍 Performing advanced deduplication analysis...")
    
    # Track items by signature for exact duplicates
    signature_map = {}
    exact_duplicates = []
    
    # Track similar items
    similar_items = []
    unique_items = []
    
    for i, item in enumerate(data):
        if not isinstance(item, dict):
            continue
        
        # Check for exact duplicates first
        signature = create_content_signature(item)
        
        if signature in signature_map:
            # Exact duplicate found
            exact_duplicates.append({
                "index": i,
                "item": item,
                "duplicate_of": signature_map[signature],
                "type": "exact"
            })
            continue
        
        signature_map[signature] = i
        
        # Check for high similarity with existing unique items
        is_similar = False
        
        for j, unique_item in enumerate(unique_items):
            similarity = calculate_detailed_similarity(item, unique_item)
            
            if similarity["overall"] >= HIGH_SIMILARITY_THRESHOLD:
                # High similarity - mark as duplicate
                similar_items.append({
                    "index": i,
                    "item": item,
                    "similar_to_index": j,
                    "similarity": similarity,
                    "type": "similar"
                })
                is_similar = True
                break
        
        if not is_similar:
            unique_items.append(item)
    
    return unique_items, exact_duplicates, similar_items

def analyze_remaining_similarity(data, threshold=ANALYSIS_SIMILARITY_THRESHOLD):
    """Analyze similarity in remaining data"""
    similar_pairs = []
    
    # Limit analysis for performance
    check_limit = min(100, len(data))
    print(f"  🔍 Analyzing similarity in first {check_limit} items...")
    
    for i in range(check_limit):
        for j in range(i + 1, check_limit):
            similarity = calculate_detailed_similarity(data[i], data[j])
            
            if similarity["overall"] >= threshold:
                similar_pairs.append({
                    "index1": i,
                    "index2": j,
                    "similarity": similarity["overall"],
                    "details": similarity
                })
    
    return similar_pairs

def save_data(data, filename):
    """Save data to JSON file"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"❌ Error saving to '{filename}': {e}")
        return False

def main():
    """Main function for integrated processing"""
    print("🚀 Integrated Function Parameters Data Processor")
    print("=" * 60)
    print(f"📊 Processing pipeline:")
    print(f"  1. Data cleaning (remove invalid items)")
    print(f"  2. Exact duplicate removal")
    print(f"  3. High similarity removal (threshold: {HIGH_SIMILARITY_THRESHOLD})")
    print(f"  4. Final similarity analysis (threshold: {ANALYSIS_SIMILARITY_THRESHOLD})")
    print("=" * 60)
    
    # Check if input file exists
    if not os.path.exists(INPUT_FILE):
        print(f"❌ Input file '{INPUT_FILE}' not found.")
        return
    
    # Load data
    print(f"📖 Loading data from '{INPUT_FILE}'...")
    data = load_data(INPUT_FILE)
    if data is None:
        return
    
    original_count = len(data)
    print(f"📊 Original data contains {original_count} items")
    
    # Step 1: Clean data
    print("\n🧹 Step 1: Cleaning data...")
    valid_items, invalid_items = clean_data(data)
    
    print(f"  ✅ Valid items: {len(valid_items)}")
    print(f"  ❌ Invalid items: {len(invalid_items)}")
    if invalid_items:
        print(f"  📝 Invalid item examples:")
        for i, invalid in enumerate(invalid_items[:3]):
            print(f"    - Index {invalid['index']}: {invalid['reason']}")
        if len(invalid_items) > 3:
            print(f"    ... and {len(invalid_items) - 3} more")
    
    # Step 2: Advanced deduplication
    print(f"\n🔄 Step 2: Advanced deduplication...")
    unique_items, exact_duplicates, similar_items = advanced_deduplicate(valid_items)
    
    total_removed = len(invalid_items) + len(exact_duplicates) + len(similar_items)
    
    print(f"  ✅ Unique items: {len(unique_items)}")
    print(f"  🔄 Exact duplicates removed: {len(exact_duplicates)}")
    print(f"  🔄 Similar items removed: {len(similar_items)}")
    print(f"  🗑️  Total removed: {total_removed}")
    
    # Show examples
    if exact_duplicates:
        print(f"  📝 Example exact duplicates:")
        for i, dup in enumerate(exact_duplicates[:3]):
            preview = dup["item"].get("current_version", "")[:60] + "..."
            print(f"    {i+1}. Index {dup['index']}: {preview}")
    
    if similar_items:
        print(f"  📝 Example similar items:")
        for i, sim in enumerate(similar_items[:3]):
            preview = sim["item"].get("current_version", "")[:60] + "..."
            similarity = sim["similarity"]["overall"]
            print(f"    {i+1}. Index {sim['index']} (similarity: {similarity:.3f}): {preview}")
    
    # Step 3: Save final data
    print(f"\n💾 Step 3: Saving final data to '{OUTPUT_FILE}'...")
    if save_data(unique_items, OUTPUT_FILE):
        print("✅ Final data saved successfully")
    else:
        print("❌ Failed to save final data")
        return
    
    # Step 4: Final similarity analysis
    if unique_items:
        print(f"\n🔍 Step 4: Final similarity analysis...")
        remaining_similar = analyze_remaining_similarity(unique_items)
        if remaining_similar:
            print(f"  ⚠ Found {len(remaining_similar)} pairs with >{ANALYSIS_SIMILARITY_THRESHOLD*100:.0f}% similarity")
            print("  💡 These might benefit from manual review")
        else:
            print("  ✅ No highly similar pairs found in remaining data")
    
    # Final summary
    print("\n🎉 Processing completed!")
    print(f"  📁 Input file: '{INPUT_FILE}' ({original_count} items)")
    print(f"  📁 Output file: '{OUTPUT_FILE}' ({len(unique_items)} items)")
    print(f"  📊 Total reduction: {total_removed} items ({total_removed/original_count*100:.1f}%)")
    print(f"  🎯 Final dataset: {len(unique_items)} high-quality unique samples")

if __name__ == "__main__":
    main()
