#!/usr/bin/env python3
"""
Quick verification script for data quality
"""

import json

def verify_data_quality(filename):
    print(f"🔍 Verifying data quality: {filename}")
    
    total_count = 0
    valid_count = 0
    errors = []
    
    with open(filename, 'r') as f:
        for line_num, line in enumerate(f, 1):
            total_count += 1
            try:
                data = json.loads(line.strip())
                
                # Check required fields
                if 'current_version' not in data or 'next_version' not in data:
                    errors.append(f"Line {line_num}: Missing required fields")
                    continue
                
                current = data['current_version']
                next_ver = data['next_version']
                
                # Check cursor markers
                if '<|cursor|>' not in current:
                    errors.append(f"Line {line_num}: Missing cursor in current_version")
                    continue
                    
                if '<|cursor|>' not in next_ver:
                    errors.append(f"Line {line_num}: Missing cursor in next_version")
                    continue
                
                # Check content consistency (excluding cursor)
                current_clean = current.replace('<|cursor|>', '')
                next_clean = next_ver.replace('<|cursor|>', '')
                
                if current_clean != next_clean:
                    errors.append(f"Line {line_num}: Content mismatch between versions")
                    continue
                
                valid_count += 1
                
            except json.JSONDecodeError as e:
                errors.append(f"Line {line_num}: JSON decode error - {e}")
                continue
    
    print(f"\n📊 Verification Results:")
    print(f"  Total samples: {total_count}")
    print(f"  Valid samples: {valid_count}")
    print(f"  Invalid samples: {len(errors)}")
    print(f"  Quality rate: {valid_count/total_count*100:.1f}%")
    
    if errors:
        print(f"\n❌ Errors found:")
        for error in errors[:5]:  # Show first 5 errors
            print(f"  {error}")
        if len(errors) > 5:
            print(f"  ... and {len(errors) - 5} more errors")
    else:
        print(f"\n✅ All samples are valid!")
    
    return valid_count == total_count

if __name__ == "__main__":
    verify_data_quality("data/cursor_prediction_training_data_clean.jsonl")
