import requests
import json
import time
import os
from datetime import datetime

# Configuration
NUM_RUNS = 1000  # Number of times to run the API call
OUTPUT_FILE = "api_responses_rename_2.json"  # Output file name
DELAY_BETWEEN_CALLS = 0  # Delay in seconds between API calls

# Define the API URL
url = "https://gfcopilot.openai.azure.com/openai/deployments/gpt-4.1-qh/chat/completions?api-version=2025-01-01-preview"

# Set the headers
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer Cjz7y5yydU3HGjJeswvPDTeegYCsM4PoTOfaklBl3rdEUBG1YUbAJQQJ99BCACHYHv6XJ3w3AAABACOGxVWw"
}

# Define the data payload
data = {
    "temperature": 1.0,
    "model": "gpt-4.1",
    "messages": [
        {
            "role": "developer",
            "content": "You are a helpful assistant."
        },
        {
            "role": "user",
            "content": """
You need to complete current_code and next_code according to the code language and scenario, and give the following data in json format for training the cursor prediction model.
The cursor position needs to be shown in the code, indicated by <|cursor|>.
The code should be in multiple lines.
The code content in `current_code` and `next_code` should be the same, but the cursor position is different. `current_code` represents the current cursor position, and `next_code` represents the predicted cursor position.
The code content should be as complex as possible and possess uniqueness.

Code Language: TypeScript
Scenario: Renaming functions/variables,After renaming a function/variable (This means the cursor should be positioned after the newly modified function/variable name at this point.), The original use of this function/variable elsewhere is no longer correct. The cursor should jump to the other code location where the function/variable is used so that the user can correctly change the function/variable name.Therefore, `current_code` should be the state of the code after a defined function/variable name has just been changed. `next_code` should be the prediction of the cursor jumping to other locations where the function/variable was used before the name change.


Reply format
{
"current_version": "current_code",
"next_version": "next_code"
},
"""
        }
    ]
}

def make_api_call():
    """Make a single API call and return the response data"""
    try:
        response = requests.post(url, headers=headers, data=json.dumps(data),)

        if response.status_code == 200:
            choices = response.json().get('choices', [])
            if choices:
                content = choices[0].get('message', {}).get('content', 'No content found')
                return {
                    "success": True,
                    "content": content,
                    "timestamp": datetime.now().isoformat(),
                    "status_code": response.status_code
                }
            else:
                return {
                    "success": False,
                    "error": "No choices found in the response",
                    "timestamp": datetime.now().isoformat(),
                    "status_code": response.status_code
                }
        else:
            return {
                "success": False,
                "error": f"HTTP {response.status_code}: {response.text}",
                "timestamp": datetime.now().isoformat(),
                "status_code": response.status_code
            }
    except Exception as e:
        return {
            "success": False,
            "error": f"Exception occurred: {str(e)}",
            "timestamp": datetime.now().isoformat(),
            "status_code": None
        }

def load_existing_data():
    """Load existing data from file if it exists"""
    try:
        if os.path.exists(OUTPUT_FILE):
            with open(OUTPUT_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []
    except Exception as e:
        print(f"⚠ Warning: Could not load existing data: {e}")
        return []

def save_data_incrementally(all_content):
    """Save data to file incrementally"""
    try:
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            json.dump(all_content, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"✗ Error saving to file: {str(e)}")
        return False

def main():
    """Main function to run multiple API calls and save results"""
    # Load existing data
    all_content = load_existing_data()
    existing_count = len(all_content)

    successful_calls = 0
    failed_calls = 0

    print(f"Starting {NUM_RUNS} API calls...")
    print(f"Results will be saved to: {OUTPUT_FILE}")
    if existing_count > 0:
        print(f"Found {existing_count} existing items in file")
    print("-" * 50)

    for i in range(NUM_RUNS):
        print(f"Making API call {i + 1}/{NUM_RUNS}...")

        response_data = make_api_call()

        # Print status
        if response_data["success"]:
            successful_calls += 1
            print(f"✓ Call {i + 1} successful")

            # Try to parse the content as JSON
            try:
                content_json = json.loads(response_data["content"])
                all_content.append(content_json)

                # Save immediately after each successful call
                if save_data_incrementally(all_content):
                    print(f"  ✓ Data saved (total items: {len(all_content)})")
                else:
                    print(f"  ✗ Failed to save data")

                # Print preview
                if "current_version" in content_json:
                    preview = content_json["current_version"]
                    if len(preview) > 80:
                        print(f"  Preview: {preview[:80]}...")
                    else:
                        print(f"  Preview: {preview}")

            except json.JSONDecodeError as e:
                print(f"  ⚠ Warning: Could not parse content as JSON: {e}")
                # Still save the raw content if JSON parsing fails
                raw_content = {"raw_content": response_data["content"]}
                all_content.append(raw_content)

                # Save immediately
                if save_data_incrementally(all_content):
                    print(f"  ✓ Raw data saved (total items: {len(all_content)})")
                else:
                    print(f"  ✗ Failed to save raw data")

        else:
            failed_calls += 1
            print(f"✗ Call {i + 1} failed: {response_data['error']}")

        # Add delay between calls (except for the last one)
        if i < NUM_RUNS - 1 and DELAY_BETWEEN_CALLS > 0:
            time.sleep(DELAY_BETWEEN_CALLS)

    # Final summary
    print("-" * 50)
    print(f"✓ All data saved to {OUTPUT_FILE}")
    print(f"Total calls: {NUM_RUNS}")
    print(f"Successful: {successful_calls}")
    print(f"Failed: {failed_calls}")
    print(f"Total items in file: {len(all_content)}")
    print(f"New items added: {len(all_content) - existing_count}")

if __name__ == "__main__":
    main()
