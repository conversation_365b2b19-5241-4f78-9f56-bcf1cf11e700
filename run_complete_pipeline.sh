#!/bin/bash
# Complete pipeline script for cursor prediction training data generation
# This script runs the entire pipeline from start to finish

echo "🚀 Starting Complete Cursor Prediction Training Data Pipeline"
echo "=============================================================="

# Check if we're in the right directory
if [ ! -d "scripts" ] || [ ! -d "data" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "   Expected structure: scripts/ and data/ directories"
    exit 1
fi

echo "📁 Project structure verified ✅"
echo ""

# Step 1: Process function parameters scenario
echo "🔧 Step 1: Processing Function Parameters Scenario..."
python3 scripts/process_function_params.py
if [ $? -ne 0 ]; then
    echo "❌ Error in function parameters processing"
    exit 1
fi
echo ""

# Step 2: Process renaming scenario
echo "🏷️  Step 2: Processing Function/Variable Renaming Scenario..."
python3 scripts/process_rename_2.py
if [ $? -ne 0 ]; then
    echo "❌ Error in renaming scenario processing"
    exit 1
fi
echo ""

# Step 3: Merge, validate, and clean
echo "🔄 Step 3: Merging, Validating, and Cleaning Data..."
python3 scripts/merge_to_jsonl.py
if [ $? -ne 0 ]; then
    echo "❌ Error in merge and cleaning process"
    exit 1
fi
echo ""

# Final verification
echo "🎯 Pipeline Completed Successfully!"
echo "=============================================================="

if [ -f "data/cursor_prediction_training_data_clean.jsonl" ]; then
    file_size=$(ls -lh data/cursor_prediction_training_data_clean.jsonl | awk '{print $5}')
    line_count=$(wc -l < data/cursor_prediction_training_data_clean.jsonl)
    echo "✅ Final training data: data/cursor_prediction_training_data_clean.jsonl"
    echo "   📊 File size: $file_size"
    echo "   📈 Sample count: $line_count"
    echo ""
    echo "🎉 Ready for cursor prediction model training!"
else
    echo "❌ Error: Final output file not found"
    exit 1
fi
