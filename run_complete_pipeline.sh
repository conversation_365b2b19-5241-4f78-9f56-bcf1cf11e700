#!/bin/bash
# Complete pipeline script for cursor prediction training data generation
# This script runs the entire pipeline from start to finish

echo "🚀 Starting Complete Cursor Prediction Training Data Pipeline"
echo "=============================================================="

# Check if we're in the right directory
if [ ! -d "scripts" ] || [ ! -d "data" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "   Expected structure: scripts/ and data/ directories"
    exit 1
fi

echo "📁 Project structure verified ✅"
echo ""

# Check if raw data files exist, generate if needed
echo "🔍 Checking for raw data files..."

missing_files=()
if [ ! -f "data/api_responses_function_params.json" ]; then
    missing_files+=("Function Parameters")
fi
if [ ! -f "data/api_responses_rename_2.json" ]; then
    missing_files+=("Renaming Scenario")
fi

if [ ${#missing_files[@]} -gt 0 ]; then
    echo "❌ Missing raw data files for: ${missing_files[*]}"
    echo ""
    echo "📊 Data Generation Required:"
    echo "   🎯 Target: ~1000 samples per scenario"
    echo "   ⏱️  Estimated time: 10-30 minutes total"
    echo "   🔗 Requires: API access for data generation"
    echo "   📝 Will generate:"
    for scenario in "${missing_files[@]}"; do
        if [ "$scenario" = "Function Parameters" ]; then
            echo "      - data/api_responses_function_params.json (~1000 samples)"
        elif [ "$scenario" = "Renaming Scenario" ]; then
            echo "      - data/api_responses_rename_2.json (~1000 samples)"
        fi
    done
    echo ""
    read -p "Generate missing data now? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Cannot proceed without raw data. Exiting."
        echo "💡 Tip: You can generate data manually using:"
        for scenario in "${missing_files[@]}"; do
            if [ "$scenario" = "Function Parameters" ]; then
                echo "   python3 scripts/generate_function_params.py"
            elif [ "$scenario" = "Renaming Scenario" ]; then
                echo "   python3 scripts/generate_rename_2.py"
            fi
        done
        exit 1
    fi

    echo ""
    echo "🚀 Starting data generation..."

    # Generate function parameters data if missing
    if [ ! -f "data/api_responses_function_params.json" ]; then
        echo "🔧 Generating Function Parameters data..."
        echo "   📝 Scenario: Function parameter modification"
        echo "   ⏱️  Estimated time: 5-15 minutes"
        python3 scripts/generate_function_params.py
        if [ $? -ne 0 ]; then
            echo "❌ Error generating function parameters data"
            exit 1
        fi

        # Show generation results
        if [ -f "data/api_responses_function_params.json" ]; then
            file_size=$(ls -lh data/api_responses_function_params.json | awk '{print $5}')
            item_count=$(python3 -c "import json; print(len(json.load(open('data/api_responses_function_params.json'))))" 2>/dev/null || echo "unknown")
            echo "   ✅ Generated: $item_count items ($file_size)"
        fi
        echo ""
    fi

    # Generate renaming scenario data if missing
    if [ ! -f "data/api_responses_rename_2.json" ]; then
        echo "🏷️  Generating Renaming Scenario data..."
        echo "   📝 Scenario: Function/variable renaming"
        echo "   ⏱️  Estimated time: 5-15 minutes"
        python3 scripts/generate_rename_2.py
        if [ $? -ne 0 ]; then
            echo "❌ Error generating renaming scenario data"
            exit 1
        fi

        # Show generation results
        if [ -f "data/api_responses_rename_2.json" ]; then
            file_size=$(ls -lh data/api_responses_rename_2.json | awk '{print $5}')
            item_count=$(python3 -c "import json; print(len(json.load(open('data/api_responses_rename_2.json'))))" 2>/dev/null || echo "unknown")
            echo "   ✅ Generated: $item_count items ($file_size)"
        fi
        echo ""
    fi

    echo "✅ Data generation completed!"
    echo ""
else
    echo "✅ All raw data files found"
    # Show existing data stats
    for file in "data/api_responses_function_params.json" "data/api_responses_rename_2.json"; do
        if [ -f "$file" ]; then
            file_size=$(ls -lh "$file" | awk '{print $5}')
            item_count=$(python3 -c "import json; print(len(json.load(open('$file'))))" 2>/dev/null || echo "unknown")
            echo "   📁 $(basename "$file"): $item_count items ($file_size)"
        fi
    done
fi

echo ""

# Step 1: Process function parameters scenario
echo "🔧 Step 1: Processing Function Parameters Scenario..."
python3 scripts/process_function_params.py
if [ $? -ne 0 ]; then
    echo "❌ Error in function parameters processing"
    exit 1
fi
echo ""

# Step 2: Process renaming scenario
echo "🏷️  Step 2: Processing Function/Variable Renaming Scenario..."
python3 scripts/process_rename_2.py
if [ $? -ne 0 ]; then
    echo "❌ Error in renaming scenario processing"
    exit 1
fi
echo ""

# Step 3: Merge, validate, and clean
echo "🔄 Step 3: Merging, Validating, and Cleaning Data..."
python3 scripts/merge_to_jsonl.py
if [ $? -ne 0 ]; then
    echo "❌ Error in merge and cleaning process"
    exit 1
fi
echo ""

# Final verification
echo "🎯 Pipeline Completed Successfully!"
echo "=============================================================="

if [ -f "data/cursor_prediction_training_data_clean.jsonl" ]; then
    file_size=$(ls -lh data/cursor_prediction_training_data_clean.jsonl | awk '{print $5}')
    line_count=$(wc -l < data/cursor_prediction_training_data_clean.jsonl)
    echo "✅ Final training data: data/cursor_prediction_training_data_clean.jsonl"
    echo "   📊 File size: $file_size"
    echo "   📈 Sample count: $line_count"
    echo ""
    echo "🎉 Ready for cursor prediction model training!"
else
    echo "❌ Error: Final output file not found"
    exit 1
fi
