#!/bin/bash
# Raw data generation script for cursor prediction training data
# This script generates the initial raw data using API calls

echo "📊 Starting Raw Data Generation for Cursor Prediction Training"
echo "=============================================================="

# Check if we're in the right directory
if [ ! -d "scripts" ] || [ ! -d "data" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "   Expected structure: scripts/ and data/ directories"
    exit 1
fi

echo "📁 Project structure verified ✅"
echo ""

# Warning about API calls
echo "⚠️  IMPORTANT NOTICE:"
echo "   This script will make API calls to generate training data"
echo "   - Function Parameters: ~1000 API calls"
echo "   - Renaming Scenario: ~1000 API calls"
echo "   - Total estimated time: 10-30 minutes (depending on API response time)"
echo "   - Make sure you have proper API access configured"
echo ""

# Confirmation
read -p "Do you want to proceed with data generation? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Data generation cancelled by user"
    exit 0
fi

echo ""

# Check existing data and ask for confirmation
existing_files=()
if [ -f "data/api_responses_function_params.json" ]; then
    existing_files+=("data/api_responses_function_params.json")
fi
if [ -f "data/api_responses_rename_2.json" ]; then
    existing_files+=("data/api_responses_rename_2.json")
fi

if [ ${#existing_files[@]} -gt 0 ]; then
    echo "⚠️  Existing raw data files found:"
    for file in "${existing_files[@]}"; do
        file_size=$(ls -lh "$file" | awk '{print $5}')
        line_count=$(wc -l < "$file" 2>/dev/null || echo "unknown")
        echo "   - $file ($file_size, ~$line_count items)"
    done
    echo ""
    read -p "Overwrite existing files? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Data generation cancelled to preserve existing files"
        exit 0
    fi
fi

echo "🚀 Starting data generation..."
echo ""

# Generate function parameters data
echo "🔧 Step 1: Generating Function Parameters Data..."
echo "   📝 Scenario: Function parameter modification"
echo "   🎯 Target: ~1000 samples"
python3 scripts/generate_function_params.py
if [ $? -ne 0 ]; then
    echo "❌ Error generating function parameters data"
    exit 1
fi

# Check if file was created and show stats
if [ -f "data/api_responses_function_params.json" ]; then
    file_size=$(ls -lh data/api_responses_function_params.json | awk '{print $5}')
    item_count=$(python3 -c "import json; print(len(json.load(open('data/api_responses_function_params.json'))))" 2>/dev/null || echo "unknown")
    echo "   ✅ Generated: data/api_responses_function_params.json ($file_size, $item_count items)"
else
    echo "   ❌ Failed to create function parameters data file"
    exit 1
fi

echo ""

# Generate renaming scenario data
echo "🏷️  Step 2: Generating Function/Variable Renaming Data..."
echo "   📝 Scenario: Function/variable renaming"
echo "   🎯 Target: ~1000 samples"
python3 scripts/generate_rename_2.py
if [ $? -ne 0 ]; then
    echo "❌ Error generating renaming scenario data"
    exit 1
fi

# Check if file was created and show stats
if [ -f "data/api_responses_rename_2.json" ]; then
    file_size=$(ls -lh data/api_responses_rename_2.json | awk '{print $5}')
    item_count=$(python3 -c "import json; print(len(json.load(open('data/api_responses_rename_2.json'))))" 2>/dev/null || echo "unknown")
    echo "   ✅ Generated: data/api_responses_rename_2.json ($file_size, $item_count items)"
else
    echo "   ❌ Failed to create renaming scenario data file"
    exit 1
fi

echo ""

# Final summary
echo "🎉 Raw Data Generation Completed Successfully!"
echo "=============================================================="
echo "✅ Generated files:"

total_items=0
for file in "data/api_responses_function_params.json" "data/api_responses_rename_2.json"; do
    if [ -f "$file" ]; then
        file_size=$(ls -lh "$file" | awk '{print $5}')
        item_count=$(python3 -c "import json; print(len(json.load(open('$file'))))" 2>/dev/null || echo "0")
        echo "   📁 $file"
        echo "      📊 Size: $file_size"
        echo "      📈 Items: $item_count"
        total_items=$((total_items + item_count))
    fi
done

echo ""
echo "📊 Total raw samples generated: $total_items"
echo ""
echo "🚀 Next steps:"
echo "   1. Run processing pipeline: ./run_complete_pipeline.sh"
echo "   2. Or process manually:"
echo "      python3 scripts/process_function_params.py"
echo "      python3 scripts/process_rename_2.py"
echo "      python3 scripts/merge_to_jsonl.py"
