#!/usr/bin/env python3
"""
Clean the JSONL file by removing invalid entries
"""

import json

def clean_jsonl(input_file: str, output_file: str):
    """Clean JSONL file by removing invalid entries"""
    print(f"🧹 Cleaning {input_file}...")
    
    total_lines = 0
    valid_lines = 0
    removed_lines = []
    
    with open(input_file, 'r', encoding='utf-8') as infile, \
         open(output_file, 'w', encoding='utf-8') as outfile:
        
        for line_num, line in enumerate(infile, 1):
            total_lines += 1
            
            try:
                data = json.loads(line.strip())
                
                # Check if both versions have cursor markers
                has_current_cursor = '<|cursor|>' in data.get('current_version', '')
                has_next_cursor = '<|cursor|>' in data.get('next_version', '')
                
                if has_current_cursor and has_next_cursor:
                    # Valid entry, write to output
                    json.dump(data, outfile, ensure_ascii=False)
                    outfile.write('\n')
                    valid_lines += 1
                else:
                    # Invalid entry, record for reporting
                    missing = []
                    if not has_current_cursor:
                        missing.append('current_version')
                    if not has_next_cursor:
                        missing.append('next_version')
                    removed_lines.append(f"Line {line_num}: Missing cursor in {', '.join(missing)}")
                
            except json.JSONDecodeError as e:
                removed_lines.append(f"Line {line_num}: JSON decode error: {e}")
    
    print(f"\n📊 Cleaning Results:")
    print(f"  Total lines processed: {total_lines}")
    print(f"  Valid lines kept: {valid_lines}")
    print(f"  Invalid lines removed: {len(removed_lines)}")
    
    if removed_lines:
        print(f"\n🗑️  Removed lines:")
        for removed in removed_lines:
            print(f"  {removed}")
    
    print(f"\n✅ Cleaned file saved as: {output_file}")
    print(f"   Final dataset: {valid_lines} valid training samples")

def main():
    clean_jsonl("cursor_prediction_training_data.jsonl", "cursor_prediction_training_data_clean.jsonl")

if __name__ == "__main__":
    main()
