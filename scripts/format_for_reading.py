#!/usr/bin/env python3
"""
Convert JSONL training data to human-readable format
Displays code samples in a clean, readable format for manual review
"""

import json
import os
import sys
from typing import Dict, Any

def format_code_sample(sample: Dict[str, Any], index: int) -> str:
    """Format a single training sample for human reading"""
    
    # Extract data
    current_version = sample.get('current_version', '')
    next_version = sample.get('next_version', '')
    scenario = sample.get('scenario', 'unknown')
    
    # Create readable format
    output = []
    output.append("=" * 80)
    output.append(f"Sample #{index + 1}")
    output.append(f"Scenario: {scenario}")
    output.append("=" * 80)
    output.append("")
    
    # Current version
    output.append("📍 CURRENT VERSION (cursor position):")
    output.append("-" * 50)
    # Replace cursor marker with a more visible indicator
    current_formatted = current_version.replace('<|cursor|>', '🔸CURSOR🔸')
    output.append(current_formatted)
    output.append("")
    
    # Next version
    output.append("🎯 NEXT VERSION (predicted cursor position):")
    output.append("-" * 50)
    # Replace cursor marker with a more visible indicator
    next_formatted = next_version.replace('<|cursor|>', '🔸CURSOR🔸')
    output.append(next_formatted)
    output.append("")
    
    return "\n".join(output)

def convert_jsonl_to_readable(input_file: str, output_file: str = None, max_samples: int = None, 
                             scenario_filter: str = None, start_index: int = 0):
    """Convert JSONL file to human-readable format"""
    
    if not os.path.exists(input_file):
        print(f"❌ Error: Input file '{input_file}' not found")
        return False
    
    # Determine output file
    if output_file is None:
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_readable.txt"
    
    try:
        samples_processed = 0
        samples_written = 0
        scenario_counts = {}
        
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8') as outfile:
            
            # Write header
            outfile.write("CURSOR PREDICTION TRAINING DATA - HUMAN READABLE FORMAT\n")
            outfile.write("=" * 80 + "\n")
            outfile.write(f"Source: {input_file}\n")
            if scenario_filter:
                outfile.write(f"Filter: {scenario_filter} scenario only\n")
            if max_samples:
                outfile.write(f"Limit: First {max_samples} samples\n")
            if start_index > 0:
                outfile.write(f"Starting from sample: {start_index + 1}\n")
            outfile.write("=" * 80 + "\n\n")
            
            for line_num, line in enumerate(infile):
                try:
                    sample = json.loads(line.strip())
                    samples_processed += 1
                    
                    # Count scenarios
                    scenario = sample.get('scenario', 'unknown')
                    scenario_counts[scenario] = scenario_counts.get(scenario, 0) + 1
                    
                    # Apply filters
                    if scenario_filter and scenario != scenario_filter:
                        continue
                    
                    # Apply start index
                    if samples_written < start_index:
                        samples_written += 1
                        continue
                    
                    # Apply max samples limit
                    if max_samples and (samples_written - start_index) >= max_samples:
                        break
                    
                    # Format and write sample
                    formatted_sample = format_code_sample(sample, samples_written)
                    outfile.write(formatted_sample)
                    outfile.write("\n\n")
                    
                    samples_written += 1
                    
                except json.JSONDecodeError as e:
                    print(f"⚠️  Warning: Invalid JSON on line {line_num + 1}: {e}")
                    continue
            
            # Write summary
            outfile.write("=" * 80 + "\n")
            outfile.write("SUMMARY\n")
            outfile.write("=" * 80 + "\n")
            outfile.write(f"Total samples processed: {samples_processed}\n")
            outfile.write(f"Samples written: {samples_written - start_index}\n")
            outfile.write(f"Scenario distribution:\n")
            for scenario, count in scenario_counts.items():
                percentage = (count / samples_processed * 100) if samples_processed > 0 else 0
                outfile.write(f"  - {scenario}: {count} samples ({percentage:.1f}%)\n")
        
        print(f"✅ Successfully converted to readable format")
        print(f"   📁 Input: {input_file} ({samples_processed} samples)")
        print(f"   📁 Output: {output_file} ({samples_written - start_index} samples)")
        
        # Show file size
        file_size = os.path.getsize(output_file)
        print(f"   📊 Output size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error converting file: {e}")
        return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Convert JSONL training data to human-readable format')
    parser.add_argument('input_file', nargs='?', default='data/cursor_prediction_training_data_clean.jsonl',
                       help='Input JSONL file (default: data/cursor_prediction_training_data_clean.jsonl)')
    parser.add_argument('-o', '--output', help='Output file (default: auto-generated)')
    parser.add_argument('-n', '--max-samples', type=int, help='Maximum number of samples to convert')
    parser.add_argument('-s', '--scenario', choices=['function_parameters', 'function_variable_renaming'],
                       help='Filter by scenario type')
    parser.add_argument('--start', type=int, default=0, help='Start from sample index (0-based)')
    parser.add_argument('--preview', action='store_true', help='Preview first 5 samples only')
    
    args = parser.parse_args()
    
    # Handle preview mode
    if args.preview:
        args.max_samples = 5
        print("🔍 Preview mode: Showing first 5 samples")
    
    print(f"🔄 Converting JSONL to readable format...")
    print(f"   📁 Input: {args.input_file}")
    if args.scenario:
        print(f"   🎯 Filter: {args.scenario} scenario")
    if args.max_samples:
        print(f"   📊 Limit: {args.max_samples} samples")
    if args.start > 0:
        print(f"   ⏭️  Starting from: sample {args.start + 1}")
    print()
    
    success = convert_jsonl_to_readable(
        input_file=args.input_file,
        output_file=args.output,
        max_samples=args.max_samples,
        scenario_filter=args.scenario,
        start_index=args.start
    )
    
    if success:
        print(f"\n🎉 Conversion completed successfully!")
        if args.output:
            print(f"   📖 Open: {args.output}")
        else:
            base_name = os.path.splitext(args.input_file)[0]
            print(f"   📖 Open: {base_name}_readable.txt")
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
