#!/usr/bin/env python3
"""
Validate the merged JSONL file
"""

import json
from collections import Counter

def validate_jsonl(filepath: str):
    """Validate JSONL file format and content"""
    print(f"🔍 Validating {filepath}...")
    
    total_lines = 0
    valid_lines = 0
    scenarios = Counter()
    errors = []
    
    required_fields = ['current_version', 'next_version', 'scenario']
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                total_lines += 1
                
                try:
                    # Parse JSON
                    data = json.loads(line.strip())
                    
                    # Check required fields
                    missing_fields = [field for field in required_fields if field not in data]
                    if missing_fields:
                        errors.append(f"Line {line_num}: Missing fields: {missing_fields}")
                        continue
                    
                    # Check cursor markers
                    if '<|cursor|>' not in data['current_version']:
                        errors.append(f"Line {line_num}: No cursor marker in current_version")
                        continue
                    
                    if '<|cursor|>' not in data['next_version']:
                        errors.append(f"Line {line_num}: No cursor marker in next_version")
                        continue
                    
                    # Count scenarios
                    scenarios[data['scenario']] += 1
                    valid_lines += 1
                    
                except json.JSONDecodeError as e:
                    errors.append(f"Line {line_num}: JSON decode error: {e}")
                
    except FileNotFoundError:
        print(f"❌ File {filepath} not found")
        return
    
    # Report results
    print(f"\n📊 Validation Results:")
    print(f"  Total lines: {total_lines}")
    print(f"  Valid lines: {valid_lines}")
    print(f"  Invalid lines: {total_lines - valid_lines}")
    
    print(f"\n📈 Scenario Distribution:")
    for scenario, count in scenarios.items():
        percentage = (count / valid_lines * 100) if valid_lines > 0 else 0
        print(f"  {scenario}: {count} samples ({percentage:.1f}%)")
    
    if errors:
        print(f"\n❌ Errors found ({len(errors)} total):")
        for error in errors[:10]:  # Show first 10 errors
            print(f"  {error}")
        if len(errors) > 10:
            print(f"  ... and {len(errors) - 10} more errors")
    else:
        print(f"\n✅ All lines are valid!")
    
    print(f"\n🎯 Summary:")
    if valid_lines == total_lines and total_lines > 0:
        print(f"  ✅ Perfect! {total_lines} valid training samples ready for use")
    else:
        print(f"  ⚠️  {valid_lines}/{total_lines} lines are valid")

def main():
    validate_jsonl("cursor_prediction_training_data.jsonl")

if __name__ == "__main__":
    main()
