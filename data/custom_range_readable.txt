CURSOR PREDICTION TRAINING DATA - HUMAN READABLE FORMAT
================================================================================
Source: data/cursor_prediction_training_data_clean.jsonl
Limit: First 5 samples
Starting from sample: 10
================================================================================

================================================================================
Sample #10
Scenario: function_parameters
================================================================================

📍 CURRENT VERSION (cursor position):
--------------------------------------------------
function fetchData(url: string🔸CURSOR🔸): Promise<Response> {
  return fetch(url);
}

const response = await fetchData('https://api.example.com/data');

🎯 NEXT VERSION (predicted cursor position):
--------------------------------------------------
function fetchData(url: string): Promise<Response> {
  return fetch(url);
}

const response = await fetchData(🔸CURSOR🔸'https://api.example.com/data');


================================================================================
Sample #11
Scenario: function_parameters
================================================================================

📍 CURRENT VERSION (cursor position):
--------------------------------------------------
function fetchData(url: string, method: string, headers: Record<string, string>): Promise<any> {
    // Implementation
    🔸CURSOR🔸
}

const response = fetchData('https://api.example.com/data', 'GET', {});
console.log(response);

🎯 NEXT VERSION (predicted cursor position):
--------------------------------------------------
function fetchData(url: string, method: string, headers: Record<string, string>): Promise<any> {
    // Implementation
    
}

const response = fetchData🔸CURSOR🔸('https://api.example.com/data', 'GET', {});
console.log(response);


================================================================================
Sample #12
Scenario: function_parameters
================================================================================

📍 CURRENT VERSION (cursor position):
--------------------------------------------------
function processData(userId: string, options: { verbose: boolean }, 🔸CURSOR🔸) {
  // Process the data for the given user
  if (options.verbose) {
    console.log(`Processing data for user: ${userId}`);
  }
  // ... additional logic ...
}

let user = getCurrentUser();
processData(user.id, { verbose: true });


🎯 NEXT VERSION (predicted cursor position):
--------------------------------------------------
function processData(userId: string, options: { verbose: boolean }, value: number) {
  // Process the data for the given user
  if (options.verbose) {
    console.log(`Processing data for user: ${userId}`);
  }
  // ... additional logic ...
}

let user = getCurrentUser();
processData(🔸CURSOR🔸user.id, { verbose: true });



================================================================================
Sample #13
Scenario: function_parameters
================================================================================

📍 CURRENT VERSION (cursor position):
--------------------------------------------------
function processUserData(userId: string, 🔸CURSOR🔸userName: string, email: string, isActive: boolean): UserProfile {
    // Validate input
    if (!userId || !userName || !email) {
        throw new Error('Missing required fields');
    }
    // Simulated data processing
    return {
        id: userId,
        name: userName,
        email: email,
        active: isActive
    };
}

const myProfile = processUserData('U123', 'Alice', '<EMAIL>', true);


🎯 NEXT VERSION (predicted cursor position):
--------------------------------------------------
function processUserData(userId: string, userName: string, email: string, isActive: boolean): UserProfile {
    // Validate input
    if (!userId || !userName || !email) {
        throw new Error('Missing required fields');
    }
    // Simulated data processing
    return {
        id: userId,
        name: userName,
        email: email,
        active: isActive
    };
}

const myProfile = processUserData(🔸CURSOR🔸'U123', 'Alice', '<EMAIL>', true);



================================================================================
Sample #14
Scenario: function_parameters
================================================================================

📍 CURRENT VERSION (cursor position):
--------------------------------------------------
function processOrder(orderId: string, quantity: number🔸CURSOR🔸) {
    // Process the order with given ID and quantity
    console.log(`Processing order #${orderId} with quantity ${quantity}`);
}

function main() {
    const id = "ORD1234";
    processOrder(id);
}

🎯 NEXT VERSION (predicted cursor position):
--------------------------------------------------
function processOrder(orderId: string, quantity: number) {
    // Process the order with given ID and quantity
    console.log(`Processing order #${orderId} with quantity ${quantity}`);
}

function main() {
    const id = "ORD1234";
    processOrder(id🔸CURSOR🔸);
}


================================================================================
SUMMARY
================================================================================
Total samples processed: 15
Samples written: 5
Scenario distribution:
  - function_parameters: 15 samples (100.0%)
