CURSOR PREDICTION TRAINING DATA - HUMAN READABLE FORMAT
================================================================================
Source: data/cursor_prediction_training_data_clean.jsonl
Limit: First 5 samples
================================================================================

================================================================================
Sample #1
Scenario: function_parameters
================================================================================

📍 CURRENT VERSION (cursor position):
--------------------------------------------------
function processUserData(userId: number, userName: string, userRole: string): UserProfile {
    // Process the user data and return user profile
    const profile: UserProfile = fetchProfile(userId);
    profile.name = userName;
    profile.role = userRole;
    return profile;
}
🔸CURSOR🔸

const userProfile = processUserData(42, "Alice");
console.log(userProfile);

🎯 NEXT VERSION (predicted cursor position):
--------------------------------------------------
function processUserData(userId: number, userName: string, userRole: string): UserProfile {
    // Process the user data and return user profile
    const profile: UserProfile = fetchProfile(userId);
    profile.name = userName;
    profile.role = userRole;
    return profile;
}

const userProfile = processUserData(42, "Alice");🔸CURSOR🔸
console.log(userProfile);


================================================================================
Sample #2
Scenario: function_parameters
================================================================================

📍 CURRENT VERSION (cursor position):
--------------------------------------------------
function processOrder(orderId: number, customerName: string🔸CURSOR🔸, priority: boolean): void {
    console.log(`Processing order #${orderId} for ${customerName}, priority: ${priority}`);
}

function main() {
    const orderId = 123;
    const name = "Alice";
    processOrder(orderId, name);
}


🎯 NEXT VERSION (predicted cursor position):
--------------------------------------------------
function processOrder(orderId: number, customerName: string, priority: boolean): void {
    console.log(`Processing order #${orderId} for ${customerName}, priority: ${priority}`);
}

function main() {
    const orderId = 123;
    const name = "Alice";
    processOrder(orderId, name🔸CURSOR🔸);
}



================================================================================
Sample #3
Scenario: function_parameters
================================================================================

📍 CURRENT VERSION (cursor position):
--------------------------------------------------
function processOrder(orderId: number, userId: string, status: string): void {
    // process the order
    console.log(`Order ${orderId} for user ${userId} has status ${status}.`);
}

function main() {
    processOrder(123, 'user_558');🔸CURSOR🔸
    console.log('Order processed.');
}


🎯 NEXT VERSION (predicted cursor position):
--------------------------------------------------
function processOrder(orderId: number, userId: string, status: string): void {
    // process the order
    console.log(`Order ${orderId} for user ${userId} has status ${status}.`);
}

function main() {
    processOrder(123, 'user_558');
    🔸CURSOR🔸console.log('Order processed.');
}



================================================================================
Sample #4
Scenario: function_parameters
================================================================================

📍 CURRENT VERSION (cursor position):
--------------------------------------------------
function processData(userId: number, 🔸CURSOR🔸userName: string, data: any[]): Result {
  // processing logic
}

const result = processData(42, ['data1', 'data2']);
console.log(result);

🎯 NEXT VERSION (predicted cursor position):
--------------------------------------------------
function processData(userId: number, userName: string, data: any[]): Result {
  // processing logic
}

const result = processData(42, 🔸CURSOR🔸['data1', 'data2']);
console.log(result);


================================================================================
Sample #5
Scenario: function_parameters
================================================================================

📍 CURRENT VERSION (cursor position):
--------------------------------------------------
function calculateSum(a: number, b: number, c: number): number {
    return a + b + c;
}

const result = calculateSum(1, 2, 🔸CURSOR🔸3);

🎯 NEXT VERSION (predicted cursor position):
--------------------------------------------------
function calculateSum(a: number, 🔸CURSOR🔸b: number, c: number): number {
    return a + b + c;
}

const result = calculateSum(1, 2, 3);


================================================================================
SUMMARY
================================================================================
Total samples processed: 6
Samples written: 5
Scenario distribution:
  - function_parameters: 6 samples (100.0%)
