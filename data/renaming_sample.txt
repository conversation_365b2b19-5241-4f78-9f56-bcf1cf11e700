CURSOR PREDICTION TRAINING DATA - HUMAN READABLE FORMAT
================================================================================
Source: data/cursor_prediction_training_data_clean.jsonl
Filter: function_variable_renaming scenario only
Limit: First 3 samples
================================================================================

================================================================================
Sample #1
Scenario: function_variable_renaming
================================================================================

📍 CURRENT VERSION (cursor position):
--------------------------------------------------
function processUserInput(input: string): void {
    let parsedInput = JSON.parse(input);
    if (parsedInput.userId) {
        let userName = getUserData(parsedInput.userId);
        console.log(userName);
    }
}

function getUserData(userId: number): string {
    // Imagine a real database call here
    return `User_${userId}`;
}

let userList = [1,2,3,4];
userList.forEach(id => {
    let name = getUser🔸CURSOR🔸Data(id);
    console.log(name);
});


🎯 NEXT VERSION (predicted cursor position):
--------------------------------------------------
function processUserInput(input: string): void {
    let parsedInput = JSON.parse(input);
    if (parsedInput.userId) {
        let userName = fetchUserData(parsedInput.userId);
        console.log(userName);
    }
}

function fetchUserData🔸CURSOR🔸(userId: number): string {
    // Imagine a real database call here
    return `User_${userId}`;
}

let userList = [1,2,3,4];
userList.forEach(id => {
    let name = getUserData(id);
    console.log(name);
});



================================================================================
Sample #2
Scenario: function_variable_renaming
================================================================================

📍 CURRENT VERSION (cursor position):
--------------------------------------------------
const fetchUserData = async (userId: string) => {
    try {
        const response = await fetch(`/api/user/${userId}`);
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Failed to fetch user data:', error);
    }
};🔸CURSOR🔸

const displayUser = async (userId: string) => {
    const user = await getUserData(userId);
    console.log('User:', user);
};

// Somewhere else in the code
fetchUserData('1234');


🎯 NEXT VERSION (predicted cursor position):
--------------------------------------------------
const fetchUserData = async (userId: string) => {
    try {
        const response = await fetch(`/api/user/${userId}`);
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Failed to fetch user data:', error);
    }
};

const displayUser = async (userId: string) => {
    const user = await getUserData(userId);
    console.log('User:', user);
};🔸CURSOR🔸

// Somewhere else in the code
fetchUserData('1234');



================================================================================
Sample #3
Scenario: function_variable_renaming
================================================================================

📍 CURRENT VERSION (cursor position):
--------------------------------------------------
const hyperDriveCoefficient = (mass: number, acceleration: number): number => {
    return mass * Math.pow(acceleration, 3);
};

function computeRocketLaunch(mass: number, acc: number) {
    const launchPower = hyperDriveCoefficient🔸CURSOR🔸(mass, acc);
    console.log(`Launch power: ${launchPower}`);
}

let rocketParams = {mass: 1000, acceleration: 25};
const launchValue = hyperDriveCoefficient(rocketParams.mass, rocketParams.acceleration);

🎯 NEXT VERSION (predicted cursor position):
--------------------------------------------------
const hyperDriveCoefficient = (mass: number, acceleration: number): number => {
    return mass * Math.pow(acceleration, 3);
};

function computeRocketLaunch(mass: number, acc: number) {
    const launchPower = hyperDriveCoefficient(mass, acc);
    console.log(`Launch power: ${launchPower}`);
}

let rocketParams = {mass: 1000, acceleration: 25};
const launchValue = hyperDriveCoefficient🔸CURSOR🔸(rocketParams.mass, rocketParams.acceleration);


================================================================================
SUMMARY
================================================================================
Total samples processed: 880
Samples written: 3
Scenario distribution:
  - function_parameters: 876 samples (99.5%)
  - function_variable_renaming: 4 samples (0.5%)
