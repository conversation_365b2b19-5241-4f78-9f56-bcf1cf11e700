#!/usr/bin/env python3
"""
Merge final JSON files, validate, clean, and convert to JSONL format
All-in-one script that produces the final cleaned training dataset
"""

import json
import os
from typing import List, Dict, Any
from collections import Counter

def load_json_file(filepath: str) -> List[Dict[str, Any]]:
    """Load JSON file and return the data"""
    if not os.path.exists(filepath):
        print(f"Warning: File {filepath} not found")
        return []
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ Loaded {len(data)} items from {filepath}")
        return data
    except Exception as e:
        print(f"❌ Error loading {filepath}: {e}")
        return []

def save_jsonl_file(data: List[Dict[str, Any]], filepath: str) -> None:
    """Save data to JSONL format"""
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            for item in data:
                json.dump(item, f, ensure_ascii=False)
                f.write('\n')
        print(f"✅ Saved {len(data)} items to {filepath}")
    except Exception as e:
        print(f"❌ Error saving {filepath}: {e}")

def add_scenario_labels(data: List[Dict[str, Any]], scenario: str) -> List[Dict[str, Any]]:
    """Add scenario label to each item"""
    labeled_data = []
    for item in data:
        labeled_item = item.copy()
        labeled_item['scenario'] = scenario
        labeled_data.append(labeled_item)
    return labeled_data

def validate_and_clean_data(data: List[Dict[str, Any]]) -> tuple[List[Dict[str, Any]], List[str]]:
    """Validate data and return cleaned data with error report"""
    valid_data = []
    errors = []
    scenarios = Counter()

    required_fields = ['current_version', 'next_version', 'scenario']

    for idx, item in enumerate(data):
        # Check required fields
        missing_fields = [field for field in required_fields if field not in item]
        if missing_fields:
            errors.append(f"Item {idx+1}: Missing fields: {missing_fields}")
            continue

        # Check cursor markers
        has_current_cursor = '<|cursor|>' in item.get('current_version', '')
        has_next_cursor = '<|cursor|>' in item.get('next_version', '')

        if not has_current_cursor or not has_next_cursor:
            missing = []
            if not has_current_cursor:
                missing.append('current_version')
            if not has_next_cursor:
                missing.append('next_version')
            errors.append(f"Item {idx+1}: Missing cursor in {', '.join(missing)}")
            continue

        # Valid item
        valid_data.append(item)
        scenarios[item['scenario']] += 1

    return valid_data, errors, scenarios

def main():
    print("🔄 Starting merge, validation, and cleaning process...")

    # File paths
    function_params_file = "api_responses_function_params_final.json"
    rename_file = "api_responses_rename_2_final.json"
    output_file = "cursor_prediction_training_data_clean.jsonl"

    # Load data from both files
    function_params_data = load_json_file(function_params_file)
    rename_data = load_json_file(rename_file)

    # Add scenario labels
    function_params_labeled = add_scenario_labels(function_params_data, "function_parameters")
    rename_labeled = add_scenario_labels(rename_data, "function_variable_renaming")

    # Merge all data
    merged_data = function_params_labeled + rename_labeled

    print(f"\n📊 Merge Summary:")
    print(f"  Function Parameters: {len(function_params_data)} items")
    print(f"  Renaming: {len(rename_data)} items")
    print(f"  Total merged: {len(merged_data)} items")

    # Validate and clean the data
    print(f"\n🔍 Validating and cleaning merged data...")
    valid_data, errors, scenarios = validate_and_clean_data(merged_data)

    # Report validation results
    print(f"\n📊 Validation Results:")
    print(f"  Total items: {len(merged_data)}")
    print(f"  Valid items: {len(valid_data)}")
    print(f"  Invalid items: {len(errors)}")

    print(f"\n📈 Scenario Distribution (valid data):")
    for scenario, count in scenarios.items():
        percentage = (count / len(valid_data) * 100) if len(valid_data) > 0 else 0
        print(f"  {scenario}: {count} samples ({percentage:.1f}%)")

    if errors:
        print(f"\n❌ Validation Errors ({len(errors)} total):")
        for error in errors[:10]:  # Show first 10 errors
            print(f"  {error}")
        if len(errors) > 10:
            print(f"  ... and {len(errors) - 10} more errors")
    else:
        print(f"\n✅ All items are valid!")

    # Save cleaned data
    if valid_data:
        save_jsonl_file(valid_data, output_file)

        # Verify output file
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            line_count = sum(1 for _ in open(output_file, 'r'))
            print(f"\n✅ Successfully created {output_file}")
            print(f"   File size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
            print(f"   Line count: {line_count:,} samples")

        # Show a sample of the output
        print(f"\n📝 Sample from {output_file}:")
        with open(output_file, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= 2:  # Show first 2 lines
                    break
                item = json.loads(line)
                print(f"  Line {i+1}: scenario='{item['scenario']}', current_version length={len(item['current_version'])}")

    # Final summary
    print(f"\n🎉 Complete pipeline finished successfully!")
    print(f"   Final training data: {output_file} ({len(valid_data)} samples) ⭐")

    if len(valid_data) == len(merged_data):
        print(f"   🎯 Perfect! All {len(merged_data)} samples are valid and ready for training")
    else:
        reduction = ((len(merged_data) - len(valid_data)) / len(merged_data) * 100)
        print(f"   🧹 Cleaned dataset: {reduction:.1f}% invalid samples removed ({len(merged_data)} → {len(valid_data)})")

if __name__ == "__main__":
    main()
