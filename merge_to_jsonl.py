#!/usr/bin/env python3
"""
Merge final JSON files and convert to JSONL format
"""

import json
import os
from typing import List, Dict, Any

def load_json_file(filepath: str) -> List[Dict[str, Any]]:
    """Load JSON file and return the data"""
    if not os.path.exists(filepath):
        print(f"Warning: File {filepath} not found")
        return []
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ Loaded {len(data)} items from {filepath}")
        return data
    except Exception as e:
        print(f"❌ Error loading {filepath}: {e}")
        return []

def save_jsonl_file(data: List[Dict[str, Any]], filepath: str) -> None:
    """Save data to JSONL format"""
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            for item in data:
                json.dump(item, f, ensure_ascii=False)
                f.write('\n')
        print(f"✅ Saved {len(data)} items to {filepath}")
    except Exception as e:
        print(f"❌ Error saving {filepath}: {e}")

def add_scenario_labels(data: List[Dict[str, Any]], scenario: str) -> List[Dict[str, Any]]:
    """Add scenario label to each item"""
    labeled_data = []
    for item in data:
        labeled_item = item.copy()
        labeled_item['scenario'] = scenario
        labeled_data.append(labeled_item)
    return labeled_data

def main():
    print("🔄 Starting merge and conversion process...")
    
    # File paths
    function_params_file = "api_responses_function_params_final.json"
    rename_file = "api_responses_rename_2_final.json"
    output_file = "cursor_prediction_training_data.jsonl"
    
    # Load data from both files
    function_params_data = load_json_file(function_params_file)
    rename_data = load_json_file(rename_file)
    
    # Add scenario labels
    function_params_labeled = add_scenario_labels(function_params_data, "function_parameters")
    rename_labeled = add_scenario_labels(rename_data, "function_variable_renaming")
    
    # Merge all data
    merged_data = function_params_labeled + rename_labeled
    
    print(f"\n📊 Merge Summary:")
    print(f"  Function Parameters: {len(function_params_data)} items")
    print(f"  Renaming: {len(rename_data)} items")
    print(f"  Total merged: {len(merged_data)} items")
    
    # Save to JSONL format
    save_jsonl_file(merged_data, output_file)
    
    # Verify the output file
    if os.path.exists(output_file):
        file_size = os.path.getsize(output_file)
        print(f"\n✅ Successfully created {output_file}")
        print(f"   File size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
        
        # Show a sample of the output
        print(f"\n📝 Sample from {output_file}:")
        with open(output_file, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= 2:  # Show first 2 lines
                    break
                item = json.loads(line)
                print(f"  Line {i+1}: scenario='{item['scenario']}', current_version length={len(item['current_version'])}")
    
    print(f"\n🎉 Merge and conversion completed successfully!")
    print(f"   Output file: {output_file}")

if __name__ == "__main__":
    main()
